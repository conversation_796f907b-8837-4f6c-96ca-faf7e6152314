package com.tem.customer.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * MCP服务配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mcp")
public class McpServerConfig {
    
    /**
     * MCP服务器配置
     */
    private Map<String, McpServer> servers;
    
    @Data
    public static class McpServer {
        /**
         * 执行命令
         */
        private String command;
        
        /**
         * 命令参数
         */
        private String[] args;
        
        /**
         * 环境变量
         */
        private Map<String, String> env;
    }
}