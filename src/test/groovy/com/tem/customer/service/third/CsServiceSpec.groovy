package com.tem.customer.service.third

import com.iplatform.common.OrderBizType
import com.iplatform.common.ResponseDto
import com.iplatform.common.utils.LogUtils
import com.tem.customer.model.dto.common.OrderInfoDto
import com.tem.customer.model.from.UserAndStandardForm
import com.tem.errand.quark.api.PartnerRankService
import com.tem.oms.api.OrderService
import com.tem.oms.dto.OrderDto
import com.tem.oms.enums.ShowStatusEnum
import com.tem.platform.api.DictService
import com.tem.platform.api.OrgService
import com.tem.platform.api.PartnerService
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.OrgDto
import com.tem.platform.api.dto.PartnerDto
import com.tem.platform.api.dto.UserDto
import com.tem.platform.api.dto.UserPartnerDto
import com.tem.platform.api.dto.UserTagDto
import org.spockframework.spring.SpringBean
import org.spockframework.spring.SpringSpy
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * CsService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class CsServiceSpec extends Specification {

    @Subject
    CsService csService

    @SpringBean
    OrderService orderService = Mock()

    @SpringBean
    UserService userService = Mock()

    @SpringBean
    PartnerService partnerService = Mock()

    @SpringBean
    OrgService orgService = Mock()

    @SpringBean
    DictService dictService = Mock()

    @SpringBean
    PartnerRankService partnerRankService = Mock()

    @SpringSpy
    LogUtils logUtils = Mock()

    def setup() {
        csService = new CsService()
        csService.orderService = orderService
        csService.userService = userService
        csService.partnerService = partnerService
        csService.orgService = orgService
        csService.dictService = dictService
        csService.partnerRankService = partnerRankService
    }

    def "获取订单信息成功"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟订单信息"
        def orderDto = createTestOrderDto(1L, OrderBizType.FLIGHT.code, "CONFIRMED")
        def orderList = [orderDto]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.success
        result.data.size() == 1
        result.data[0].id == 1L
        result.data[0].bizType == OrderBizType.FLIGHT
        result.data[0].trip == "上海-北京"
        result.data[0].orderShowStatus == ShowStatusEnum.CONFIRMED.flightMessage
    }

    def "获取订单信息失败 - 用户不存在"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户不存在"
        userService.getUserBaseInfo(userId) >> ResponseDto.error("用户不存在")

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.notSuccess
    }

    def "获取订单信息失败 - 订单为空"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟订单为空"
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success([])

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.success
        result.data.isEmpty()
    }

    def "获取订单信息 - 过滤已取消订单"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟订单信息（包含已取消订单）"
        def confirmedOrder = createTestOrderDto(1L, OrderBizType.FLIGHT.code, "CONFIRMED")
        def canceledOrder = createTestOrderDto(2L, OrderBizType.FLIGHT.code, "CANCELED")
        def orderList = [confirmedOrder, canceledOrder]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.success
        result.data.size() == 1
        result.data[0].id == 1L
    }

    def "获取订单信息 - 过滤无出发时间订单"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟订单信息（包含无出发时间订单）"
        def validOrder = createTestOrderDto(1L, OrderBizType.FLIGHT.code, "CONFIRMED")
        def invalidOrder = createTestOrderDto(2L, OrderBizType.FLIGHT.code, "CONFIRMED")
        invalidOrder.setTravelStartTime(null)
        def orderList = [validOrder, invalidOrder]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.success
        result.data.size() == 1
        result.data[0].id == 1L
    }

    def "获取订单信息 - 限制返回数量"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟多个订单信息"
        def orderList = []
        for (int i = 1; i <= 25; i++) {
            def order = createTestOrderDto(i, OrderBizType.FLIGHT.code, "CONFIRMED")
            order.setTravelStartTime(LocalDateTime.now().plusDays(i))
            orderList.add(order)
        }
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.success
        result.data.size() == 20
    }

    def "获取订单信息 - 按时间排序"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟订单信息（不同时间）"
        def order1 = createTestOrderDto(1L, OrderBizType.FLIGHT.code, "CONFIRMED")
        order1.setTravelStartTime(LocalDateTime.now().plusDays(1))
        def order2 = createTestOrderDto(2L, OrderBizType.FLIGHT.code, "CONFIRMED")
        order2.setTravelStartTime(LocalDateTime.now().plusDays(3))
        def order3 = createTestOrderDto(3L, OrderBizType.FLIGHT.code, "CONFIRMED")
        order3.setTravelStartTime(LocalDateTime.now().plusDays(2))
        def orderList = [order1, order2, order3]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果按时间倒序排列"
        result.success
        result.data.size() == 3
        result.data[0].id == 2L // 最晚的订单
        result.data[1].id == 3L
        result.data[2].id == 1L // 最早的订单
    }

    def "获取用户和差标基本信息成功"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setFullname("测试用户")
        userDto.setMobile("13800138000")
        userDto.setEmail("<EMAIL>")
        userDto.setGender(0)
        userDto.setVipLevel("VIP")
        userDto.setPartnerId(456L)
        userDto.setOrgId(789L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟企业信息"
        def partnerDto = new PartnerDto()
        partnerDto.setId(456L)
        partnerDto.setName("测试企业")
        partnerDto.setRemarks("企业备注")
        partnerDto.setSalerId(999L)
        partnerDto.setManagerId(888L)
        partnerDto.setPartnerChannel("DIRECT")
        partnerService.findById(456L) >> ResponseDto.success(partnerDto)

        and: "模拟可选企业列表"
        def selectablePartners = [
            new UserPartnerDto(partnerId: 456L, partnerName: "测试企业")
        ]
        partnerService.findSelectablePartner(userId) >> ResponseDto.success(selectablePartners)

        and: "模拟销售和经理信息"
        def salerUser = new UserDto()
        salerUser.setFullname("销售经理")
        userService.getUserBaseInfo(999L) >> ResponseDto.success(salerUser)

        def managerUser = new UserDto()
        managerUser.setFullname("企业经理")
        userService.getUserBaseInfo(888L) >> ResponseDto.success(managerUser)

        and: "模拟字典服务"
        def channelMap = ["DIRECT": "直销渠道"]
        dictService.getCodesMap("PARTNER_CHANNEL") >> ResponseDto.success(channelMap)

        and: "模拟组织信息"
        def orgDto = new OrgDto()
        orgDto.setPath("/1/2/3")
        orgService.getById(789L) >> ResponseDto.success(orgDto)
        orgService.getOrgNameByPath("/1/2/3") >> ResponseDto.success("总公司/分公司/部门")

        and: "模拟用户标签"
        def tags = [
            new UserTagDto(tagId: 1L, tagName: "重要客户", status: 1),
            new UserTagDto(tagId: 2L, tagName: "潜在客户", status: 0)
        ]
        userService.findUserTags(userId) >> ResponseDto.success(tags)

        and: "模拟员工职级"
        partnerRankService.getEmpRank(456L, userId) >> ResponseDto.success("LEVEL_3")
        dictService.getPartnerCodeTxt(456L, "TM_EMPLOYEE_LEVEL", "LEVEL_3") >> ResponseDto.success("高级")

        when: "调用获取用户和差标基本信息方法"
        def result = csService.getUserAndStandardInfo(userId)

        then: "验证返回结果"
        result.userId == userId
        result.userName == "测试用户"
        result.mobile == "13800138000"
        result.email == "<EMAIL>"
        result.gender == "男"
        result.vipLevel == "VIP"
        result.partnerId == 456L
        result.partnerName == "测试企业"
        result.partnerRemarks == "企业备注"
        result.salerName == "销售经理"
        result.managerName == "企业经理"
        result.partnerChannelName == "直销渠道"
        result.orgPathNames == "总公司/分公司/部门"
        result.empLevelName == "高级"
        result.tags.size() == 1
        result.tags[0].tagName == "重要客户"
    }

    def "获取用户和差标基本信息 - 员工职级为空"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setFullname("测试用户")
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟企业信息"
        def partnerDto = new PartnerDto()
        partnerDto.setId(456L)
        partnerDto.setName("测试企业")
        partnerService.findById(456L) >> ResponseDto.success(partnerDto)

        and: "模拟可选企业列表"
        def selectablePartners = [
            new UserPartnerDto(partnerId: 456L, partnerName: "测试企业")
        ]
        partnerService.findSelectablePartner(userId) >> ResponseDto.success(selectablePartners)

        and: "模拟员工职级为空"
        partnerRankService.getEmpRank(456L, userId) >> ResponseDto.success(null)

        when: "调用获取用户和差标基本信息方法"
        def result = csService.getUserAndStandardInfo(userId)

        then: "验证返回结果"
        result.userId == userId
        result.userName == "测试用户"
        result.partnerId == 456L
        result.partnerName == "测试企业"
        result.empLevelName == null
    }

    def "获取用户和差标基本信息 - 销售信息为空"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setFullname("测试用户")
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟企业信息（无销售和经理）"
        def partnerDto = new PartnerDto()
        partnerDto.setId(456L)
        partnerDto.setName("测试企业")
        partnerDto.setSalerId(null)
        partnerDto.setManagerId(null)
        partnerService.findById(456L) >> ResponseDto.success(partnerDto)

        and: "模拟可选企业列表"
        def selectablePartners = [
            new UserPartnerDto(partnerId: 456L, partnerName: "测试企业")
        ]
        partnerService.findSelectablePartner(userId) >> ResponseDto.success(selectablePartners)

        and: "模拟员工职级"
        partnerRankService.getEmpRank(456L, userId) >> ResponseDto.success("LEVEL_3")
        dictService.getPartnerCodeTxt(456L, "TM_EMPLOYEE_LEVEL", "LEVEL_3") >> ResponseDto.success("高级")

        when: "调用获取用户和差标基本信息方法"
        def result = csService.getUserAndStandardInfo(userId)

        then: "验证返回结果"
        result.userId == userId
        result.userName == "测试用户"
        result.partnerId == 456L
        result.partnerName == "测试企业"
        result.salerName == null
        result.managerName == null
        result.empLevelName == "高级"
    }

    def "获取用户和差标基本信息 - 组织信息为空"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setFullname("测试用户")
        userDto.setPartnerId(456L)
        userDto.setOrgId(null) // 组织ID为空
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟企业信息"
        def partnerDto = new PartnerDto()
        partnerDto.setId(456L)
        partnerDto.setName("测试企业")
        partnerService.findById(456L) >> ResponseDto.success(partnerDto)

        and: "模拟可选企业列表"
        def selectablePartners = [
            new UserPartnerDto(partnerId: 456L, partnerName: "测试企业")
        ]
        partnerService.findSelectablePartner(userId) >> ResponseDto.success(selectablePartners)

        and: "模拟员工职级"
        partnerRankService.getEmpRank(456L, userId) >> ResponseDto.success("LEVEL_3")
        dictService.getPartnerCodeTxt(456L, "TM_EMPLOYEE_LEVEL", "LEVEL_3") >> ResponseDto.success("高级")

        when: "调用获取用户和差标基本信息方法"
        def result = csService.getUserAndStandardInfo(userId)

        then: "验证返回结果"
        result.userId == userId
        result.userName == "测试用户"
        result.partnerId == 456L
        result.partnerName == "测试企业"
        result.orgPathNames == null
        result.empLevelName == "高级"
    }

    def "获取用户和差标基本信息 - 用户标签为空"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setFullname("测试用户")
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟企业信息"
        def partnerDto = new PartnerDto()
        partnerDto.setId(456L)
        partnerDto.setName("测试企业")
        partnerService.findById(456L) >> ResponseDto.success(partnerDto)

        and: "模拟可选企业列表"
        def selectablePartners = [
            new UserPartnerDto(partnerId: 456L, partnerName: "测试企业")
        ]
        partnerService.findSelectablePartner(userId) >> ResponseDto.success(selectablePartners)

        and: "模拟用户标签为空"
        userService.findUserTags(userId) >> ResponseDto.success(null)

        and: "模拟员工职级"
        partnerRankService.getEmpRank(456L, userId) >> ResponseDto.success("LEVEL_3")
        dictService.getPartnerCodeTxt(456L, "TM_EMPLOYEE_LEVEL", "LEVEL_3") >> ResponseDto.success("高级")

        when: "调用获取用户和差标基本信息方法"
        def result = csService.getUserAndStandardInfo(userId)

        then: "验证返回结果"
        result.userId == userId
        result.userName == "测试用户"
        result.partnerId == 456L
        result.partnerName == "测试企业"
        result.empLevelName == "高级"
        result.tags.isEmpty()
    }

    @Unroll
    def "处理不同业务类型的订单 - #bizType"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟订单信息"
        def orderDto = createTestOrderDto(1L, bizTypeCode, "CONFIRMED")
        def orderList = [orderDto]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证返回结果"
        result.success
        result.data.size() == 1
        result.data[0].bizType == OrderBizType.getBizTypeCode(bizTypeCode)

        where:
        bizTypeCode << [OrderBizType.FLIGHT.code, OrderBizType.HOTEL.code, OrderBizType.TRAIN.code, 
                       OrderBizType.INSURANCE.code, OrderBizType.INTL_FLIGHT.code, OrderBizType.GENERAL.code]
    }

    @Unroll
    def "测试性别转换 - #gender -> #expectedGender"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setFullname("测试用户")
        userDto.setGender(gender)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟企业信息"
        def partnerDto = new PartnerDto()
        partnerDto.setId(456L)
        partnerDto.setName("测试企业")
        partnerService.findById(456L) >> ResponseDto.success(partnerDto)

        and: "模拟可选企业列表"
        def selectablePartners = [
            new UserPartnerDto(partnerId: 456L, partnerName: "测试企业")
        ]
        partnerService.findSelectablePartner(userId) >> ResponseDto.success(selectablePartners)

        and: "模拟员工职级"
        partnerRankService.getEmpRank(456L, userId) >> ResponseDto.success("LEVEL_3")
        dictService.getPartnerCodeTxt(456L, "TM_EMPLOYEE_LEVEL", "LEVEL_3") >> ResponseDto.success("高级")

        when: "调用获取用户和差标基本信息方法"
        def result = csService.getUserAndStandardInfo(userId)

        then: "验证性别转换"
        result.gender == expectedGender

        where:
        gender | expectedGender
        0      | "男"
        1      | "女"
        2      | null
    }

    def "测试行程信息显示规则"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟多程航班订单"
        def orderDto = createTestOrderDto(1L, OrderBizType.FLIGHT.code, "CONFIRMED")
        def flightOrderDetailDtos = [
            new com.tem.oms.dto.FlightOrderDetailDto(fromCityName: "上海", toCityName: "北京"),
            new com.tem.oms.dto.FlightOrderDetailDto(fromCityName: "北京", toCityName: "天津"),
            new com.tem.oms.dto.FlightOrderDetailDto(fromCityName: "天津", toCityName: "杭州")
        ]
        orderDto.getOrderDetail().setFlightOrderDetailDtos(flightOrderDetailDtos)
        def orderList = [orderDto]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证行程信息合并规则"
        result.success
        result.data.size() == 1
        result.data[0].trip == "上海-北京-天津-杭州"
    }

    def "测试行程信息显示规则 - 不连续的城市"() {
        given: "测试参数"
        def userId = 123L

        and: "模拟用户信息"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(456L)
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        and: "模拟不连续的航班订单"
        def orderDto = createTestOrderDto(1L, OrderBizType.FLIGHT.code, "CONFIRMED")
        def flightOrderDetailDtos = [
            new com.tem.oms.dto.FlightOrderDetailDto(fromCityName: "上海", toCityName: "北京"),
            new com.tem.oms.dto.FlightOrderDetailDto(fromCityName: "天津", toCityName: "杭州"),
            new com.tem.oms.dto.FlightOrderDetailDto(fromCityName: "上海", toCityName: "北京")
        ]
        orderDto.getOrderDetail().setFlightOrderDetailDtos(flightOrderDetailDtos)
        def orderList = [orderDto]
        orderService.queryOrderListByCs(456L, userId, _) >> ResponseDto.success(orderList)

        when: "调用获取订单信息方法"
        def result = csService.getOrderInfo(userId)

        then: "验证行程信息不连续时的显示"
        result.success
        result.data.size() == 1
        result.data[0].trip == "上海-北京,天津-杭州,上海-北京"
    }

    def "创建测试订单DTO的辅助方法"() {
        given: "测试参数"
        def orderId = 1L
        def bizType = OrderBizType.FLIGHT.code
        def status = "CONFIRMED"

        when: "调用辅助方法"
        def result = createTestOrderDto(orderId, bizType, status)

        then: "验证返回结果"
        result.id == orderId
        result.bizType == bizType
        result.orderShowStatus == status
        result.travelStartTime != null
    }

    private OrderDto createTestOrderDto(Long orderId, Integer bizType, String status) {
        def orderDto = new OrderDto()
        orderDto.setId(orderId)
        orderDto.setBizType(bizType)
        orderDto.setOrderShowStatus(status)
        orderDto.setTravelStartTime(LocalDateTime.now().plusDays(1))
        orderDto.setTotalAmount(1000.0)
        orderDto.setOrderTravellerNames("测试旅客")

        def orderDetail = new com.tem.oms.dto.OrderDetailDto()
        
        if (bizType == OrderBizType.FLIGHT.code) {
            def flightDetail = new com.tem.oms.dto.FlightOrderDetailDto()
            flightDetail.setFromCityName("上海")
            flightDetail.setToCityName("北京")
            orderDetail.setFlightOrderDetailDtos([flightDetail])
        } else if (bizType == OrderBizType.HOTEL.code) {
            def hotelDetail = new com.tem.oms.dto.HotelOrderDetailDto()
            hotelDetail.setHotelName("测试酒店")
            hotelDetail.setHotelAddress("测试地址")
            hotelDetail.setCheckInDate(LocalDate.now())
            hotelDetail.setCheckOutDate(LocalDate.now().plusDays(1))
            orderDetail.setHotelOrderDetailDto(hotelDetail)
        } else if (bizType == OrderBizType.TRAIN.code) {
            def trainDetail = new com.tem.oms.dto.TrainOrderDetailDto()
            trainDetail.setFromStation("上海站")
            trainDetail.setArriveStation("北京站")
            orderDetail.setTrainOrderDetailDtos([trainDetail])
        } else if (bizType == OrderBizType.INSURANCE.code) {
            def insuranceDetail = new com.tem.oms.dto.InsuranceProductInfoDto()
            insuranceDetail.setInsuranceName("测试保险")
            orderDto.setInsuranceProductInfoDtos([insuranceDetail])
        } else if (bizType == OrderBizType.INTL_FLIGHT.code) {
            def intlFlightDetail = new com.tem.oms.dto.IntlFlightOrderDetailDto()
            intlFlightDetail.setFromCityName("上海")
            intlFlightDetail.setToCityName("纽约")
            orderDetail.setIntlFlightOrderDetailDtos([intlFlightDetail])
        } else if (bizType == OrderBizType.GENERAL.code) {
            def generalDetail = new com.tem.oms.dto.GeneralProductInfoDto()
            generalDetail.setServiceName("测试服务")
            orderDetail.setGeneralProductInfoDto(generalDetail)
        }

        orderDto.setOrderDetail(orderDetail)
        return orderDto
    }
}